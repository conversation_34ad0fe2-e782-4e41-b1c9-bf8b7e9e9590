import jsPDF from 'jspdf';

interface ResumeData {
  header: {
    name: string;
    title: string;
    contact: string[];
  };
  leftColumn: {
    education: string[];
    skills: string[];
    links: string[];
    coursework: string[];
    additional: string[];
  };
  rightColumn: {
    experience: string[];
    research: string[];
    awards: string[];
    publications: string[];
  };
}

// Parse resume content to match ResumeRenderer structure exactly
const parseResumeToStructure = (text: string): ResumeData => {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line);

  const resumeData: ResumeData = {
    header: { name: '', title: '', contact: [] },
    leftColumn: { education: [], skills: [], links: [], coursework: [], additional: [] },
    rightColumn: { experience: [], research: [], awards: [], publications: [] }
  };

  let currentSection = '';
  let isHeaderSection = true;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Detect name (usually first line, all caps or title case)
    if (i === 0 && (line.match(/^[A-Z\s]+$/) || line.match(/^[A-Z][a-z\s]+$/))) {
      resumeData.header.name = line;
      continue;
    }

    // Detect title/role (second line, contains job-related keywords)
    if (i === 1 && line.match(/engineer|developer|manager|analyst|specialist|coordinator|director|consultant|designer|architect|scientist|researcher|lead|senior|principal|intern|associate|executive/i)) {
      resumeData.header.title = line;
      continue;
    }

    // Detect contact info
    if (isHeaderSection && (
      line.match(/@|phone:|email:|linkedin:|github:|location:|tel:|www\.|http|portfolio:|website:/i) ||
      line.match(/^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/) ||
      line.match(/^(phone|email|linkedin|github|location|address|website|portfolio):/i) ||
      line.match(/\.(com|org|net|edu|io|dev)/)
    )) {
      resumeData.header.contact.push(line);
      continue;
    }

    // Detect section headers
    if (line.match(/^(EDUCATION|SKILLS|KEY SKILLS|TECHNICAL SKILLS|PROGRAMMING SKILLS|LINKS|SOCIAL|COURSEWORK|COURSE WORK|EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE|RESEARCH|AWARDS|ACHIEVEMENTS|PUBLICATIONS|PROJECTS|ADDITIONAL|CERTIFICATIONS|SUMMARY|PROFESSIONAL SUMMARY|OBJECTIVE)/i)) {
      currentSection = line.toLowerCase().replace(/[^a-z]/g, '');
      isHeaderSection = false;
      continue;
    }

    // Also detect section headers with different formatting (e.g., "## EXPERIENCE")
    const sectionMatch = line.match(/^#+\s*(EDUCATION|SKILLS|KEY SKILLS|TECHNICAL SKILLS|PROGRAMMING SKILLS|LINKS|SOCIAL|COURSEWORK|COURSE WORK|EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE|RESEARCH|AWARDS|ACHIEVEMENTS|PUBLICATIONS|PROJECTS|ADDITIONAL|CERTIFICATIONS|SUMMARY|PROFESSIONAL SUMMARY|OBJECTIVE)/i);
    if (sectionMatch) {
      currentSection = sectionMatch[1].toLowerCase().replace(/[^a-z]/g, '');
      isHeaderSection = false;
      continue;
    }

    // Add content to appropriate sections
    if (currentSection && line) {
      switch (currentSection) {
        case 'education':
          resumeData.leftColumn.education.push(line);
          break;
        case 'skills':
        case 'keyskills':
        case 'technicalskills':
        case 'programmingskills':
          resumeData.leftColumn.skills.push(line);
          break;
        case 'links':
        case 'social':
          resumeData.leftColumn.links.push(line);
          break;
        case 'coursework':
          resumeData.leftColumn.coursework.push(line);
          break;
        case 'experience':
        case 'workexperience':
        case 'professionalexperience':
          resumeData.rightColumn.experience.push(line);
          break;
        case 'research':
          resumeData.rightColumn.research.push(line);
          break;
        case 'awards':
        case 'achievements':
          resumeData.rightColumn.awards.push(line);
          break;
        case 'publications':
          resumeData.rightColumn.publications.push(line);
          break;
        case 'projects':
        case 'additional':
        case 'certifications':
          resumeData.leftColumn.additional.push(line);
          break;
        case 'summary':
        case 'professionalsummary':
        case 'objective':
          // Add summary to the top of experience section
          resumeData.rightColumn.experience.unshift(line);
          break;
        default:
          // If we don't know where to put it, add to experience (main content)
          resumeData.rightColumn.experience.push(line);
      }
    }
  }

  return resumeData;
};

// Clean text for PDF (remove HTML and markdown)
const cleanText = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text || '');
  }

  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\*\*(.*?)\*\*/g, '$1')  // Remove markdown bold
    .replace(/__(.*?)__/g, '$1')      // Remove markdown underline
    .replace(/\*(.*?)\*/g, '$1')      // Remove markdown italic
    .replace(/`(.*?)`/g, '$1')        // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Convert links to text
    .replace(/[•●◦○⚬]/g, '•')         // Normalize bullet points
    .replace(/[-–—]/g, '-')           // Normalize dashes
    .trim();
};

export const downloadResumeAsPDF = (resumeContent: string): void => {
  try {
    const resumeData = parseResumeToStructure(resumeContent);
    const pdf = new jsPDF();
    
    // Page dimensions
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 20;
    const leftColumnWidth = (pageWidth - 3 * margin) * 0.35; // 35% for left column
    const rightColumnWidth = (pageWidth - 3 * margin) * 0.65; // 65% for right column
    const leftColumnX = margin;
    const rightColumnX = margin + leftColumnWidth + margin;
    
    let y = margin;

    // Colors matching Deedy CV theme
    const colors = {
      primary: [30, 64, 175], // Blue-900
      secondary: [107, 114, 128], // Gray-500
      text: [17, 24, 39], // Gray-900
      lightText: [75, 85, 99] // Gray-600
    };

    // Helper function to add text with word wrapping
    const addWrappedText = (text: string, x: number, yPos: number, maxWidth: number, fontSize: number = 10): number => {
      pdf.setFontSize(fontSize);
      const lines = pdf.splitTextToSize(cleanText(text), maxWidth);

      for (let i = 0; i < lines.length; i++) {
        if (yPos > pageHeight - margin) {
          pdf.addPage();
          yPos = margin;
        }
        pdf.text(lines[i], x, yPos);
        yPos += fontSize * 0.6; // Better line height
      }

      return yPos + 3; // Add some spacing after text block
    };

    // Helper function to check if we need a new page
    const checkPageBreak = (currentY: number, requiredSpace: number = 20): number => {
      if (currentY + requiredSpace > pageHeight - margin) {
        pdf.addPage();
        return margin;
      }
      return currentY;
    };

    // Header Section
    if (resumeData.header.name) {
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
      pdf.text(resumeData.header.name, pageWidth / 2, y, { align: 'center' });
      y += 15;
    }

    if (resumeData.header.title) {
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
      pdf.text(resumeData.header.title, pageWidth / 2, y, { align: 'center' });
      y += 10;
    }

    // Contact information
    if (resumeData.header.contact.length > 0) {
      pdf.setFontSize(10);
      pdf.setTextColor(colors.lightText[0], colors.lightText[1], colors.lightText[2]);
      const contactText = resumeData.header.contact.map(cleanText).join(' | ');
      const contactLines = pdf.splitTextToSize(contactText, pageWidth - 2 * margin);
      
      for (let i = 0; i < contactLines.length; i++) {
        pdf.text(contactLines[i], pageWidth / 2, y, { align: 'center' });
        y += 6;
      }
    }

    // Add separator line
    y += 10;
    pdf.setDrawColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
    pdf.line(margin, y, pageWidth - margin, y);
    y += 15;

    // Helper function to add section header
    const addSectionHeader = (title: string, x: number, yPos: number, maxWidth: number): number => {
      yPos = checkPageBreak(yPos, 15);

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
      const upperTitle = title.toUpperCase();
      pdf.text(upperTitle, x, yPos);

      // Add underline that spans the full width
      pdf.setDrawColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
      pdf.setLineWidth(0.8);
      pdf.line(x, yPos + 2, x + maxWidth, yPos + 2);

      return yPos + 15;
    };

    // Helper function to add bullet points
    const addBulletPoint = (text: string, x: number, yPos: number, maxWidth: number): number => {
      yPos = checkPageBreak(yPos, 15);

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);

      // Add blue bullet point (matching Deedy CV style)
      pdf.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
      pdf.circle(x + 3, yPos - 2, 1, 'F');

      // Add text with proper wrapping
      const textY = addWrappedText(text, x + 8, yPos, maxWidth - 8, 10);
      return textY + 2; // Add small spacing between bullet points
    };

    // Track Y positions for both columns
    let leftY = y;
    let rightY = y;

    // Left Column
    // Education
    if (resumeData.leftColumn.education.length > 0) {
      leftY = addSectionHeader('Education', leftColumnX, leftY, leftColumnWidth);
      resumeData.leftColumn.education.forEach(item => {
        leftY = addBulletPoint(item, leftColumnX, leftY, leftColumnWidth);
      });
      leftY += 5;
    }

    // Skills
    if (resumeData.leftColumn.skills.length > 0) {
      leftY = addSectionHeader('Skills', leftColumnX, leftY, leftColumnWidth);
      resumeData.leftColumn.skills.forEach(item => {
        leftY = addBulletPoint(item, leftColumnX, leftY, leftColumnWidth);
      });
      leftY += 5;
    }

    // Links
    if (resumeData.leftColumn.links.length > 0) {
      leftY = addSectionHeader('Links', leftColumnX, leftY, leftColumnWidth);
      resumeData.leftColumn.links.forEach(item => {
        leftY = addBulletPoint(item, leftColumnX, leftY, leftColumnWidth);
      });
      leftY += 5;
    }

    // Coursework
    if (resumeData.leftColumn.coursework.length > 0) {
      leftY = addSectionHeader('Coursework', leftColumnX, leftY, leftColumnWidth);
      resumeData.leftColumn.coursework.forEach(item => {
        leftY = addBulletPoint(item, leftColumnX, leftY, leftColumnWidth);
      });
      leftY += 5;
    }

    // Additional
    if (resumeData.leftColumn.additional.length > 0) {
      leftY = addSectionHeader('Additional', leftColumnX, leftY, leftColumnWidth);
      resumeData.leftColumn.additional.forEach(item => {
        leftY = addBulletPoint(item, leftColumnX, leftY, leftColumnWidth);
      });
    }

    // Right Column
    // Experience
    if (resumeData.rightColumn.experience.length > 0) {
      rightY = addSectionHeader('Experience', rightColumnX, rightY, rightColumnWidth);
      resumeData.rightColumn.experience.forEach(item => {
        rightY = addBulletPoint(item, rightColumnX, rightY, rightColumnWidth);
      });
      rightY += 5;
    }

    // Research
    if (resumeData.rightColumn.research.length > 0) {
      rightY = addSectionHeader('Research', rightColumnX, rightY, rightColumnWidth);
      resumeData.rightColumn.research.forEach(item => {
        rightY = addBulletPoint(item, rightColumnX, rightY, rightColumnWidth);
      });
      rightY += 5;
    }

    // Awards
    if (resumeData.rightColumn.awards.length > 0) {
      rightY = addSectionHeader('Awards', rightColumnX, rightY, rightColumnWidth);
      resumeData.rightColumn.awards.forEach(item => {
        rightY = addBulletPoint(item, rightColumnX, rightY, rightColumnWidth);
      });
      rightY += 5;
    }

    // Publications
    if (resumeData.rightColumn.publications.length > 0) {
      rightY = addSectionHeader('Publications', rightColumnX, rightY, rightColumnWidth);
      resumeData.rightColumn.publications.forEach(item => {
        rightY = addBulletPoint(item, rightColumnX, rightY, rightColumnWidth);
      });
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `resume_${timestamp}.pdf`;

    // Save the PDF
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF resume');
  }
};

export const downloadCoverLetterAsPDF = (coverLetterContent: string): void => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;

    let y = margin;

    // Colors matching Deedy CV theme
    const colors = {
      primary: [30, 64, 175], // Blue-900
      secondary: [107, 114, 128], // Gray-500
      text: [17, 24, 39], // Gray-900
      lightText: [75, 85, 99] // Gray-600
    };

    // Helper function to add text with word wrapping
    const addWrappedText = (text: string, x: number, yPos: number, maxWidth: number, fontSize: number = 11): number => {
      pdf.setFontSize(fontSize);
      const lines = pdf.splitTextToSize(cleanText(text), maxWidth);

      for (let i = 0; i < lines.length; i++) {
        if (yPos > pageHeight - margin) {
          pdf.addPage();
          yPos = margin;
        }
        pdf.text(lines[i], x, yPos);
        yPos += fontSize * 0.6;
      }

      return yPos + 5;
    };

    // Title
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
    pdf.text('COVER LETTER', pageWidth / 2, y, { align: 'center' });
    y += 20;

    // Add separator line
    pdf.setDrawColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
    pdf.line(margin, y, pageWidth - margin, y);
    y += 15;

    // Content
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);

    const paragraphs = coverLetterContent.split('\n\n');
    paragraphs.forEach(paragraph => {
      if (paragraph.trim()) {
        y = addWrappedText(paragraph, margin, y, maxWidth, 11);
        y += 5; // Space between paragraphs
      }
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `cover_letter_${timestamp}.pdf`;

    // Save the PDF
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating cover letter PDF:', error);
    throw new Error('Failed to generate PDF cover letter');
  }
};

export const downloadEmailAsPDF = (emailContent: string): void => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;

    let y = margin;

    // Colors matching Deedy CV theme
    const colors = {
      primary: [30, 64, 175], // Blue-900
      secondary: [107, 114, 128], // Gray-500
      text: [17, 24, 39], // Gray-900
      lightText: [75, 85, 99] // Gray-600
    };

    // Helper function to add text with word wrapping
    const addWrappedText = (text: string, x: number, yPos: number, maxWidth: number, fontSize: number = 11): number => {
      pdf.setFontSize(fontSize);
      const lines = pdf.splitTextToSize(cleanText(text), maxWidth);

      for (let i = 0; i < lines.length; i++) {
        if (yPos > pageHeight - margin) {
          pdf.addPage();
          yPos = margin;
        }
        pdf.text(lines[i], x, yPos);
        yPos += fontSize * 0.6;
      }

      return yPos + 5;
    };

    // Title
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
    pdf.text('EMAIL TEMPLATE', pageWidth / 2, y, { align: 'center' });
    y += 20;

    // Add separator line
    pdf.setDrawColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
    pdf.line(margin, y, pageWidth - margin, y);
    y += 15;

    // Content
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);

    const lines = emailContent.split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        y = addWrappedText(line, margin, y, maxWidth, 11);
      } else {
        y += 8; // Empty line spacing
      }
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `email_template_${timestamp}.pdf`;

    // Save the PDF
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating email PDF:', error);
    throw new Error('Failed to generate PDF email template');
  }
};
