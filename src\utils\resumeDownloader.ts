import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import React from 'react';
import { createRoot } from 'react-dom/client';
import ResumeRenderer from '@/components/ResumeRenderer';

/**
 * Enhanced Resume PDF Downloader - EXACT UI MATCHING
 *
 * NEW APPROACH: Uses the same ResumeRenderer component as the UI and print function
 * This ensures 100% visual consistency between UI display, print, and PDF download
 *
 * FEATURES:
 * 1. ✅ Uses actual ResumeRenderer React component (same as UI/print)
 * 2. ✅ Preserves all CSS styling and Deedy CV formatting
 * 3. ✅ Maintains exact visual consistency across all outputs
 * 4. ✅ Professional two-column layout with proper spacing
 * 5. ✅ All typography, colors, and styling preserved
 * 6. ✅ ATS-friendly formatting maintained
 */

interface ResumeData {
  header: {
    name: string;
    title: string;
    contact: string[];
  };
  leftColumn: {
    education: string[];
    skills: string[];
    links: string[];
    coursework: string[];
    additional: string[];
  };
  rightColumn: {
    experience: string[];
    research: string[];
    awards: string[];
    publications: string[];
  };
}

// Parse resume content to match ResumeRenderer structure exactly
const parseResumeToStructure = (text: string): ResumeData => {
  console.log('🔍 Parsing resume content...');

  if (!text || typeof text !== 'string') {
    console.warn('⚠️ Invalid resume content provided');
    return {
      header: { name: '', title: '', contact: [] },
      leftColumn: { education: [], skills: [], links: [], coursework: [], additional: [] },
      rightColumn: { experience: [], research: [], awards: [], publications: [] }
    };
  }

  const lines = text.split('\n').map(line => line.trim()).filter(line => line);
  console.log('📝 Total lines to parse:', lines.length);

  const resumeData: ResumeData = {
    header: { name: '', title: '', contact: [] },
    leftColumn: { education: [], skills: [], links: [], coursework: [], additional: [] },
    rightColumn: { experience: [], research: [], awards: [], publications: [] }
  };

  let currentSection = '';
  let isHeaderSection = true;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // More flexible name detection
    if (i < 3 && !resumeData.header.name && line.length > 2 && line.length < 50) {
      // Check if it looks like a name (not a section header or long text)
      if (!line.match(/^(EDUCATION|SKILLS|EXPERIENCE|WORK|PROFESSIONAL|SUMMARY|OBJECTIVE|CONTACT|EMAIL|PHONE)/i) &&
          (line.match(/^[A-Z\s]+$/) || line.match(/^[A-Z][a-z\s]+$/) || line.match(/^[A-Z][a-zA-Z\s]+$/))) {
        resumeData.header.name = line;
        console.log('📛 Found name:', line);
        continue;
      }
    }

    // More flexible title detection
    if (i < 5 && !resumeData.header.title && line.length > 5 && line.length < 100) {
      if (line.match(/engineer|developer|manager|analyst|specialist|coordinator|director|consultant|designer|architect|scientist|researcher|lead|senior|principal|intern|associate|executive|student|graduate|professional/i)) {
        resumeData.header.title = line;
        console.log('💼 Found title:', line);
        continue;
      }
    }

    // Detect contact info
    if (isHeaderSection && (
      line.match(/@|phone:|email:|linkedin:|github:|location:|tel:|www\.|http|portfolio:|website:/i) ||
      line.match(/^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/) ||
      line.match(/^(phone|email|linkedin|github|location|address|website|portfolio):/i) ||
      line.match(/\.(com|org|net|edu|io|dev)/)
    )) {
      resumeData.header.contact.push(line);
      continue;
    }

    // Detect section headers
    if (line.match(/^(EDUCATION|SKILLS|KEY SKILLS|TECHNICAL SKILLS|PROGRAMMING SKILLS|LINKS|SOCIAL|COURSEWORK|COURSE WORK|EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE|RESEARCH|AWARDS|ACHIEVEMENTS|PUBLICATIONS|PROJECTS|ADDITIONAL|CERTIFICATIONS|SUMMARY|PROFESSIONAL SUMMARY|OBJECTIVE)/i)) {
      currentSection = line.toLowerCase().replace(/[^a-z]/g, '');
      isHeaderSection = false;
      continue;
    }

    // Also detect section headers with different formatting (e.g., "## EXPERIENCE")
    const sectionMatch = line.match(/^#+\s*(EDUCATION|SKILLS|KEY SKILLS|TECHNICAL SKILLS|PROGRAMMING SKILLS|LINKS|SOCIAL|COURSEWORK|COURSE WORK|EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE|RESEARCH|AWARDS|ACHIEVEMENTS|PUBLICATIONS|PROJECTS|ADDITIONAL|CERTIFICATIONS|SUMMARY|PROFESSIONAL SUMMARY|OBJECTIVE)/i);
    if (sectionMatch) {
      currentSection = sectionMatch[1].toLowerCase().replace(/[^a-z]/g, '');
      isHeaderSection = false;
      continue;
    }

    // Add content to appropriate sections
    if (currentSection && line) {
      console.log(`📂 Adding to ${currentSection}:`, line.substring(0, 50));
      switch (currentSection) {
        case 'education':
          resumeData.leftColumn.education.push(line);
          break;
        case 'skills':
        case 'keyskills':
        case 'technicalskills':
        case 'programmingskills':
          resumeData.leftColumn.skills.push(line);
          break;
        case 'links':
        case 'social':
          resumeData.leftColumn.links.push(line);
          break;
        case 'coursework':
          resumeData.leftColumn.coursework.push(line);
          break;
        case 'experience':
        case 'workexperience':
        case 'professionalexperience':
          resumeData.rightColumn.experience.push(line);
          break;
        case 'research':
          resumeData.rightColumn.research.push(line);
          break;
        case 'awards':
        case 'achievements':
          resumeData.rightColumn.awards.push(line);
          break;
        case 'publications':
          resumeData.rightColumn.publications.push(line);
          break;
        case 'projects':
        case 'additional':
        case 'certifications':
          resumeData.leftColumn.additional.push(line);
          break;
        case 'summary':
        case 'professionalsummary':
        case 'objective':
          // Add summary to the top of experience section
          resumeData.rightColumn.experience.unshift(line);
          break;
        default:
          // If we don't know where to put it, add to experience (main content)
          resumeData.rightColumn.experience.push(line);
      }
    } else if (!currentSection && !isHeaderSection && line.length > 10) {
      // If no section is detected but we have content, add it to experience as fallback
      console.log('📝 Adding to experience (fallback):', line.substring(0, 50));
      resumeData.rightColumn.experience.push(line);
    }
  }

  console.log('📊 Parsing complete. Sections found:');
  console.log('  - Name:', resumeData.header.name || 'Not found');
  console.log('  - Title:', resumeData.header.title || 'Not found');
  console.log('  - Contact items:', resumeData.header.contact.length);
  console.log('  - Skills:', resumeData.leftColumn.skills.length);
  console.log('  - Education:', resumeData.leftColumn.education.length);
  console.log('  - Experience:', resumeData.rightColumn.experience.length);

  return resumeData;
};

// Clean text for PDF (remove HTML and markdown)
const cleanText = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text || '');
  }

  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\*\*(.*?)\*\*/g, '$1')  // Remove markdown bold
    .replace(/__(.*?)__/g, '$1')      // Remove markdown underline
    .replace(/\*(.*?)\*/g, '$1')      // Remove markdown italic
    .replace(/`(.*?)`/g, '$1')        // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Convert links to text
    .replace(/[•●◦○⚬]/g, '•')         // Normalize bullet points
    .replace(/[-–—]/g, '-')           // Normalize dashes
    .trim();
};

// Render ResumeRenderer component to HTML (same as print function)
const renderResumeComponentToHTML = async (content: string): Promise<string> => {
  return new Promise((resolve) => {
    // Create a temporary container
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.width = '210mm'; // A4 width
    tempContainer.style.backgroundColor = 'white';
    document.body.appendChild(tempContainer);

    // Create the ResumeRenderer component (same as UI and print)
    const component = React.createElement(ResumeRenderer, {
      content,
      className: 'pdf-version'
    });

    // Render the component
    const root = createRoot(tempContainer);
    root.render(component);

    // Wait for rendering to complete
    setTimeout(() => {
      const html = tempContainer.innerHTML;

      // Clean up
      root.unmount();
      document.body.removeChild(tempContainer);

      resolve(html);
    }, 100);
  });
};

// New PDF download function that matches UI exactly
export const downloadResumeAsPDF = async (resumeContent: string): Promise<void> => {
  try {
    console.log('📄 Starting PDF generation with UI matching...');
    console.log('📝 Resume content length:', resumeContent?.length || 0);

    if (!resumeContent || resumeContent.trim().length === 0) {
      throw new Error('Resume content is empty or undefined');
    }

    // Create a temporary container for rendering
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '210mm'; // A4 width
    container.style.minHeight = '297mm'; // A4 height
    container.style.backgroundColor = 'white';
    container.style.padding = '20mm';
    container.style.boxSizing = 'border-box';

    // Add all the CSS styles that are used in the UI
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
      /* Import the same Deedy CV styles used in the UI */
      .deedy-resume {
        max-width: 100%;
        background: white;
        padding: 0;
        font-family: 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-size: 15px;
        line-height: 1.6;
        color: #111827;
      }

      .deedy-header {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid #e5e7eb;
      }

      .deedy-name {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        color: #111827;
        text-align: center;
        letter-spacing: 0.05em;
      }

      .deedy-title {
        font-size: 1.5rem;
        font-weight: 400;
        color: #6b7280;
        margin-bottom: 1.5rem;
        text-align: center;
      }

      .deedy-contact {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        font-size: 1rem;
        color: #4b5563;
      }

      .deedy-contact-item {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        background: #f9fafb;
        border-radius: 9999px;
        border: 1px solid #e5e7eb;
      }

      .deedy-columns {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 2.5rem;
      }

      .deedy-left-column {
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }

      .deedy-right-column {
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }

      .deedy-section {
        margin-bottom: 2rem;
      }

      .deedy-section-header {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e40af;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #d1d5db;
        text-transform: uppercase;
        letter-spacing: 0.1em;
      }

      .deedy-section-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .deedy-bullet-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        border-radius: 0.375rem;
      }

      .deedy-bullet-point {
        flex-shrink: 0;
        width: 0.5rem;
        height: 0.5rem;
        background: #1e40af;
        border-radius: 50%;
        margin-top: 0.75rem;
        margin-right: 1rem;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      }

      .deedy-bullet-content {
        color: #374151;
        font-size: 15px;
        line-height: 1.6;
        flex: 1;
      }

      .deedy-text-content {
        color: #374151;
        font-size: 15px;
        line-height: 1.6;
        margin-bottom: 0.75rem;
      }

      .deedy-company-name {
        font-weight: 700;
        color: #1e40af;
        font-size: 1.125rem;
      }

      /* Print optimizations */
      @media print {
        .deedy-resume {
          padding: 0;
          max-width: none;
          font-size: 12pt;
          line-height: 1.5;
        }

        .deedy-columns {
          grid-template-columns: 1fr 2fr;
        }

        .deedy-name {
          font-size: 2rem;
        }

        .deedy-title {
          font-size: 1.25rem;
        }
      }
    `;

    container.appendChild(styleSheet);
    document.body.appendChild(container);

    // Render the ResumeRenderer component in the container
    const component = React.createElement(ResumeRenderer, {
      content: resumeContent,
      className: 'pdf-version'
    });

    const root = createRoot(container);
    root.render(component);

    // Wait for rendering to complete
    await new Promise(resolve => setTimeout(resolve, 500));

    // Convert the rendered HTML to canvas
    const canvas = await html2canvas(container, {
      scale: 2, // Higher resolution
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: container.offsetWidth,
      height: container.offsetHeight,
      scrollX: 0,
      scrollY: 0
    });

    // Clean up the DOM
    root.unmount();
    document.body.removeChild(container);

    // Create PDF from canvas
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgData = canvas.toDataURL('image/png');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Calculate dimensions to fit the page
    const canvasAspectRatio = canvas.height / canvas.width;
    const pdfAspectRatio = pdfHeight / pdfWidth;

    let imgWidth = pdfWidth;
    let imgHeight = pdfWidth * canvasAspectRatio;

    // If the image is too tall, scale it down
    if (imgHeight > pdfHeight) {
      imgHeight = pdfHeight;
      imgWidth = pdfHeight / canvasAspectRatio;
    }

    // Center the image on the page
    const x = (pdfWidth - imgWidth) / 2;
    const y = (pdfHeight - imgHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `resume_${timestamp}.pdf`;

    // Save the PDF
    pdf.save(filename);

    console.log('✅ PDF generated successfully with UI matching:', filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF resume');
  }
};

// Simple cover letter PDF generator
export const downloadCoverLetterAsPDF = (coverLetterContent: string): void => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;

    let y = margin;

    // Title
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(30, 64, 175); // Blue theme
    pdf.text('COVER LETTER', pageWidth / 2, y, { align: 'center' });
    y += 20;

    // Add separator line
    pdf.setDrawColor(107, 114, 128);
    pdf.line(margin, y, pageWidth - margin, y);
    y += 15;

    // Content
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(17, 24, 39);

    const paragraphs = coverLetterContent.split('\n\n');
    paragraphs.forEach(paragraph => {
      if (paragraph.trim() && y < pageHeight - margin - 20) {
        const lines = pdf.splitTextToSize(paragraph.trim(), maxWidth);
        lines.forEach((line: string) => {
          if (y < pageHeight - margin - 20) {
            pdf.text(line, margin, y);
            y += 6;
          }
        });
        y += 8; // Paragraph spacing
      }
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `cover_letter_${timestamp}.pdf`;
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating cover letter PDF:', error);
    throw new Error('Failed to generate PDF cover letter');
  }
};

// Simple email template PDF generator
export const downloadEmailAsPDF = (emailContent: string): void => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;

    let y = margin;

    // Title
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(30, 64, 175); // Blue theme
    pdf.text('EMAIL TEMPLATE', pageWidth / 2, y, { align: 'center' });
    y += 20;

    // Add separator line
    pdf.setDrawColor(107, 114, 128);
    pdf.line(margin, y, pageWidth - margin, y);
    y += 15;

    // Content
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(17, 24, 39);

    const lines = emailContent.split('\n');
    lines.forEach(line => {
      if (y < pageHeight - margin - 20) {
        if (line.trim()) {
          const wrappedLines = pdf.splitTextToSize(line.trim(), maxWidth);
          wrappedLines.forEach((wrappedLine: string) => {
            if (y < pageHeight - margin - 20) {
              pdf.text(wrappedLine, margin, y);
              y += 6;
            }
          });
        } else {
          y += 8; // Empty line spacing
        }
      }
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `email_template_${timestamp}.pdf`;
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating email PDF:', error);
    throw new Error('Failed to generate PDF email template');
  }
};

// Test function for debugging
export const testPDFGeneration = (): void => {
  const testContent = `John Doe
Software Developer
<EMAIL> | (555) 123-4567 | LinkedIn: linkedin.com/in/johndoe

SKILLS
JavaScript, React, Node.js, Python, SQL

EDUCATION
Bachelor of Science in Computer Science
University of Technology, 2020

EXPERIENCE
Software Developer | Tech Company | 2020-2023
• Developed web applications using React and Node.js
• Improved application performance by 40%
• Collaborated with cross-functional teams`;

  console.log('🧪 Running PDF generation test...');
  downloadResumeAsPDF(testContent);
};
